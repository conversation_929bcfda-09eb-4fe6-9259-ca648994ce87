// Copyright (c) 2024, <PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt
/* eslint-disable */

frappe.query_reports["CIBIL Data"] = {
	"filters": [
		{
			"label": "To Date",
			"fieldtype": "Date",
			"fieldname": "to_date"
		},
		{
			"label": "Source",
			"fieldtype": "Select",
			"fieldname": "source",
			"options": ["", "DSA", "Direct"]
		},
		{
			"label": "Report Type",
			"fieldtype": "Select",
			"fieldname": "report_type",
			"options": ["", "Consumer", "Commercial"],
			"reqd": 1,
			"on_change": function (query_report) {
				let report_type = frappe.query_report.get_filter_value("report_type");
				let segment_filter = frappe.query_report.get_filter("choose_segment");
				let download_btn = frappe.query_report.get_filter("download_txt");
				let choose_cic_filter = frappe.query_report.get_filter("choose_cic");

				if (report_type === "Commercial") {
					// Show segment and CIC
					segment_filter.df.hidden = 0;
					segment_filter.df.reqd = 1;
					choose_cic_filter.df.hidden = 0;
				} else {
					// Hide segment and CIC
					segment_filter.df.hidden = 1;
					segment_filter.df.reqd = 0;
					// choose_cic_filter.df.hidden = 1;
					frappe.query_report.set_filter_value("choose_segment", "");
					// frappe.query_report.set_filter_value("choose_cic", "");
					download_btn.df.hidden = 1;
					download_btn.refresh();
				}

				segment_filter.refresh();
				// choose_cic_filter.refresh();
				frappe.query_report.refresh();
			}
		},
		{
			"label": "Choose Your CIC",
			"fieldtype": "Select",
			"fieldname": "choose_cic",
			"options": ["", "CIBIL", "CRIF", "EXPERIAN","EQUIFAX"],
			"default":"CIBIL",
			"reqd": 1,

		},
		{
			"label": "Choose Segment",
			"fieldtype": "Select",
			"fieldname": "choose_segment",
			"options": ["", "HD", "BS", "AS", "RS", "CR", "GS", "SS", "CD", "TS", "Sorting"],
			"on_change": function (query_report) {
				let segment = frappe.query_report.get_filter_value("choose_segment");
				let download_btn = frappe.query_report.get_filter("download_txt");
				let report_type = frappe.query_report.get_filter_value("report_type");

				if (segment === "Sorting" && report_type === "Commercial") {
					download_btn.df.hidden = 0;
				} else {
					download_btn.df.hidden = 1;
				}
				download_btn.refresh();
				frappe.query_report.refresh();
			}
		},
		{
			"label": "Download TXT",
			"fieldtype": "Button",
			"fieldname": "download_txt",
			"hidden": 1, // hidden by default
			"click": function () {
				let segment = frappe.query_report.get_filter_value("choose_segment");
				if (segment === "Sorting") {
					window.open(
						"/api/method/nbfc.nbfc.report.cibil_data.cibil_data.download_cibil_segments_txt"
					);
				} else {
					frappe.msgprint("TXT download is only available for Sorting segment.");
				}
			}
		}
	]
};
