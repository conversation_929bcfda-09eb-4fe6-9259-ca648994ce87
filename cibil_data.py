# Copyright (c) 2024, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt
from frappe.utils.response import build_response
from io import BytesIO
import frappe
import os
from frappe.utils.file_manager import save_file
from frappe.utils import today, getdate,nowdate
from frappe.utils import get_site_path
from frappe import _
import re
from frappe.utils import cint, flt
from nbfc.nbfc.report.agewise_overdue.agewise_overdue import get_data as get_agewise_overdue_data
from nbfc.nbfc.custom_scripts.loan_repayment.loan_repayment import _calculate_amounts




def execute(filters=""):
	columns = []
	data = []
	if filters.get("report_type"):
		if filters.get("report_type") == "Consumer":
		
			columns = [
				{
					'fieldname' : 'consumer',
					'label' : 'Consumer Name',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'dob',
					'label' : 'Date of Birth',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'gender',
					'label' : 'Gender',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'pan_no',
					'label' : 'Income Tax Id Number',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'passport_no',
					'label' : 'Passport Number',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'passport_issue_date',
					'label' : 'Passport Issue Date',
					'fieldtype' : 'Date',
					'width' : 150
				},
				{
					'fieldname' : 'passport_expiry_date',
					'label' : 'Passport Expiry Date',
					'fieldtype' : 'Date',
					'width' : 150
				},
				{
					'fieldname' : 'voter_id',
					'label' : 'Voter ID Number',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'driving_license',
					'label' : 'Driving License Number',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'driving_license_issue_date',
					'label' : 'Driving License Issue Date',
					'fieldtype' : 'Date',
					'width' : 150
				},
				{
					'fieldname' : 'driving_issue_expiry_date',
					'label' : 'Driving License Expiry Date',
					'fieldtype' : 'Date',
					'width' : 150
				},
				{
					'fieldname' : 'ration_card_number',
					'label' : 'Ration Card Number',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'universal_id_number',
					'label' : 'Universal ID Number',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'additional_id1',
					'label' : 'Additional ID  #1',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'additional_id2',
					'label' : 'Additional ID  #2',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'mobile_no',
					'label' : 'Telephone No.Mobile',
					'fieldtype' : 'Phone',
					'width' : 150
				},
				{
					'fieldname' : 'landline_no',
					'label' : 'Telephone No.Residence',
					'fieldtype' : 'Phone',
					'width' : 150
				},
				{
					'fieldname' : 'telephone_no_office',
					'label' : 'Telephone No. Office',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'extension_office',
					'label' : 'Extension Office',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'telephone_no_other',
					'label' : 'Telephone No. Other',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'extension_other',
					'label' : 'Extension Other',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'email_id1',
					'label' : 'Email ID 1',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'email_id2',
					'label' : 'Email ID 2',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'address',
					'label' : 'Address Line 1',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'state_code',
					'label' : 'State Code 1',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'pin_code',
					'label' : 'PIN Code 1',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'address_category',
					'label' : 'Address Category 1',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'residence_code',
					'label' : 'Residence Code 1',
					'fieldtype' : 'Int',
					'width' : 150
				},
				{
					'fieldname' : 'address2',
					'label' : 'Address Line 2',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'state_code2',
					'label' : 'State Code 2',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'pin_code2',
					'label' : 'PIN Code 2',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'address_category2',
					'label' : 'Address Category 2',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'residence_code2',
					'label' : 'Residence Code 2',
					'fieldtype' : 'Int',
					'width' : 150
				},
				{
					'fieldname' : 'member_code',
					'label' : 'Curr/New Member Code',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'member_short_name',
					'label' : 'Current/New Member Short Name',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'current_account_no',
					'label' : 'Current/New Account No.',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'account_type',
					'label' : 'Account Type',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'ownership_indicator',
					'label' : 'Ownership Indicator',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'disbursed',
					'label' : 'Date Opened/Disbursed',
					'fieldtype' : 'Date',
					'width' : 150
				},
				{
					'fieldname' : 'date_of_last_payment',
					'label' : 'Date of Last Payment',
					'fieldtype' : 'Date',
					'width' : 150
				},
				{
					'fieldname' : 'date_closed',
					'label' : 'Date Closed',
					'fieldtype' : 'Date',
					'width' : 150
				},
				{
					'fieldname' : 'date_reported',
					'label' : 'Date Reported',
					'fieldtype' : 'Date',
					'width' : 150
				},
				{
					'fieldname' : 'sanctioned_amount',
					'label' : 'High Credit/Sanctioned Amt',
					'fieldtype' : 'Currency',
					'width' : 150
				},
				{
					'fieldname' : 'current_balance',
					'label' : 'Current Balance',
					'fieldtype' : 'Currency',
					'width' : 150
				},
				{
					'fieldname' : 'amount_overdue',
					'label' : 'Amt Overdue',
					'fieldtype' : 'Currency',
					'width' : 150
				},
				{
					'fieldname' : 'dpd',
					'label' : 'No of Days Past Due',
					'fieldtype' : 'Int',
					'width' : 150
				},
				{
					'fieldname' : 'old_mbr_code',
					'label' : 'Old Mbr Code',
					'fieldtype' : 'Int',
					'width' : 150
				},
				{
					'fieldname' : 'old_mbr_short_name',
					'label' : 'Old Mbr Short Name',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'old_acc_no',
					'label' : 'Old Acc No',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'old_acc_type',
					'label' : 'Old Acc Type',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'old_ownership',
					'label' : 'Old Ownership Indicator',
					'fieldtype' : 'Data',
					'width' : 150
				},


				{
					'fieldname' : 'suit_filed',
					'label' : 'Suit Filed/ Wilful Default',
					'fieldtype' : 'Int',
					'width' : 150
				},
				{
					'fieldname' : 'ws',
					'label' : 'Credit Facility Status',
					'fieldtype' : 'Int',
					'width' : 150
				},
				{
					'fieldname' : 'asset_class',
					'label' : 'Asset Classification',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'value_of_collateral',
					'label' : 'Value of Collateral',
					'fieldtype' : 'Currency',
					'width' : 150
				},
				{
					'fieldname' : 'type_of_collateral',
					'label' : 'Type of Collateral',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'credit_limit',
					'label' : 'Credit Limit',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'cash_limit',
					'label' : 'Cash Limit',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'roi',
					'label' : 'Rate Of Interest',
					'fieldtype' : 'Percent',
					'width' : 150
				},
				{
					'fieldname' : 'tenure',
					'label' : 'Repayment Tenure',
					'fieldtype' : 'Int',
					'width' : 150
				},
				{
					'fieldname' : 'emi_amount',
					'label' : 'EMI Amount',
					'fieldtype' : 'Currency',
					'width' : 150
				},
				{
					'fieldname' : 'written_off_amt',
					'label' : 'Written-off Amount(Total)',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'written_off_principal_amt',
					'label' : 'Written-off Principal Amount',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'settlement_amt',
					'label' : 'Settlement Amt',
					'fieldtype' : 'Currency',
					'width' : 150
				},
				{
					'fieldname' : 'frequency',
					'label' : 'Payment Frequency',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'actual_payment_amt',
					'label' : 'Actual Payment Amt',
					'fieldtype' : 'Currency',
					'width' : 150
				},
				{
					'fieldname' : 'occupation_code',
					'label' : 'Occupation Code',
					'fieldtype' : 'Int',
					'width' : 150
				},
				{
					'fieldname' : 'income',
					'label' : 'Income ',
					'fieldtype' : 'Currency',
					'width' : 150
				},
				{
					'fieldname' : 'ng_income_indicator',
					'label' : 'Net/Gross Income Indicator',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'ma_income_indicator',
					'label' : 'Monthly/Anual Income Indicator',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'ckyc',
					'label' : 'CKYC',
					'fieldtype' : 'Data',
					'width' : 150
				},
				{
					'fieldname' : 'nrega_number',
					'label' : 'NREGA Card Number',
					'fieldtype' : 'Data',
					'width' : 150
				}
			]
			account_type_category={
				"Auto Loan":"01",
				"Housing Loan":"02",
				"Property Loan":"03",
				"Loan Against Shares/Securities":"04",
				"Personal Loan":"05",
				"Consumer Loan":"06",
				"Gold Loan":"07",
				"Education Loan":"08"
			}
			loans = ""
			# if filters.get("source"):
			# 	if filters.get("source") == "DSA":
			# 		loans = frappe.db.get_all("Loan",fields=["name"],filters=[["status","!=","Closed"],["docstatus","=",1],["source_type","=","DSA"],["workflow_state", "!=", "Y-Everything Cleared but NOC hold"]])
			# 	else:
			# 		loans = frappe.db.get_all("Loan",fields=["name"],filters=[["status","!=","Closed"],["docstatus","=",1],["source_type","=","Direct"],["workflow_state", "!=", "Y-Everything Cleared but NOC hold"]])
			# else:
			# 	loans = frappe.db.get_all("Loan",fields=["name"],filters=[["docstatus","=",1],["status","!=","Closed"],["workflow_state", "!=", "Y-Everything Cleared but NOC hold"]])
			excluded_states = [
				"T-Terminated Via Repo",
				"X-Write off & Terminated",
				"Z-Terminated",
				"Y-Everything Cleared but NOC hold"
			]

			if filters.get("source"):
				source_filter = [["source_type", "=", filters.get("source")]]
			else:
				source_filter = []

			loans = frappe.db.get_all(
				"Loan",
				fields=["name"],
				filters=[
					["status", "!=", "Closed"],
					["docstatus", "=", 1],
					*source_filter,
					["workflow_state", "not in", excluded_states]
				]
			)
			agewise_filters = {
				"show_terminated_loans": False,
				"emi_type": "All",
				"applicant_name": None
			}

			agewise_data = get_agewise_overdue_data(agewise_filters)

			
			overdue_map = {}
			for row in agewise_data:
				loan_no = row.get("loan_no")
				if loan_no:
					if loan_no not in overdue_map:
						overdue_map[loan_no] = {
							"balance": 0,
							"dpd":0
							}
				overdue_map[loan_no]["balance"] += flt(row.get("balance", 0))
				overdue_map[loan_no]["dpd"] = max(overdue_map[loan_no]["dpd"], flt(row.get("over_days", 0)))  # ✅ max

				
			data = []

			loan_filters = ''

			if filters.get("to_date"):
				loan_filters = f""" AND posting_date < '{filters.get("to_date")}' """ 

			settings = frappe.get_single("NBFC Setting")
			cic = filters.get("choose_cic") or "CIBIL"

			member_code_map = {
				"CIBIL": settings.cibil_member_code,
				"CRIF": settings.crif_member_code,
				"EXPERIAN": settings.experian_member_code,
				"EQUIFAX": settings.equifax_member_code
			}

			member_code = member_code_map.get(cic, settings.cibil_member_code)
			member_short_name = settings.member_short_name  

			for l in loans:
				loan = frappe.get_doc("Loan",l.name)
				cust = frappe.get_doc("Customer",loan.applicant)
				if cust.customer_type == "Company":
					continue
				pan = ""
				address = frappe.get_doc("Address",cust.customer_primary_address) if cust.customer_primary_address else ""
				# loan_amounts = frappe.db.sql(f"""
				# 	SELECT SUM(payable_principal_amount+interest_amount-paid_principal_amount-paid_interest_amount) as overdue_amount,
				# 	SUM(payable_principal_amount - paid_principal_amount) as current_balance,
				# 	SUM(payable_principal_amount) as accrued_principal,
				# 	SUM(CASE WHEN (payable_principal_amount+interest_amount-paid_principal_amount-paid_interest_amount) > 0 THEN DATEDIFF( CURDATE(),posting_date) ELSE 0 END) as dpd
				# 	FROM `tabLoan Interest Accrual` where loan = '{loan.name}' {loan_filters} and docstatus = 1 GROUP BY loan
				# 	""",as_dict=1)

				pan = ""
				aadhar = ""
				voter = ""
				passport = ""
				dl = ""
				ration_card = ""

				for d in cust.documents:
					if d.unmasked_kyc_no and d.unmasked_kyc_no not in ["N/A", "NA"]:
						cleaned_no = d.unmasked_kyc_no.replace("/", "").replace("-", "")
						
						if d.document_type == "PAN Card":
							pan = cleaned_no
						elif d.document_type == "Aadhar Card":
							aadhar = cleaned_no
						elif d.document_type == "Voter Id":
							voter = cleaned_no
						elif d.document_type == "Passport":
							passport = cleaned_no
						elif d.document_type == "Driving License":
							dl = cleaned_no



				mobile = cust.mobile_no if cust.mobile_no else "+91-"
				mobile = mobile.replace("+91-","")
				# frappe.throw(repr(loan_amounts))
				account_type_category_code=account_type_category.get(loan.get("product_category"), " ")
				add_cat = "04"

				if address and address.address_type:
					add_cat = "01" if address.address_type == "Permanent" else "02" if address.address_type == "Personal" else "03" if address.address_type == "Office" or address.address_type == "Shop" else "04" 
				# Fetch last EMI repayment date
				last_repayment = frappe.get_all(
					"Loan Repayment",
					filters={"against_loan": loan.name, "docstatus": 1},
					fields=["posting_date","against_loan"],
					order_by="posting_date desc",
					limit_page_length=1
				)

				last_payment_date = last_repayment[0]["posting_date"] if last_repayment else ""

				# paid_row = frappe.db.sql(
				# 	f"""
				# 	SELECT SUM(paid_principal_amount) AS paid_principal
				# 	FROM `tabLoan Interest Accrual`
				# 	WHERE loan = '{loan.name}'
				# 	{loan_filters}
				# 	AND docstatus = 1
				# 	""",
				# 	as_dict=True,
				# )
				# paid_principal = flt(paid_row[0].paid_principal) if paid_row else 0.0
				# current_balance = flt(loan.loan_amount) - paid_principal
				

				overdue = overdue_map.get(loan.name)  
				amount_overdue=0
				dpd=0
				if overdue:
					amount_overdue = overdue.get("balance", 0)
					dpd=overdue.get("dpd",0)

				# date= filters.get("to_date")
				# amounts = _calculate_amounts(loan.name,date )
				# current_balance= amounts["future_principal"]+amount_overdue
				has_accrued = any(row.is_accrued for row in loan.repayment_schedule)

				calc_date = filters.get("to_date") 

				if has_accrued:
					amounts = _calculate_amounts(loan.name, calc_date)
					current_balance = flt(amounts.get("future_principal", 0)) + flt(amount_overdue)
				else:
					current_balance = flt(loan.loan_amount)
					
				data.append({
					"consumer": cust.customer_name.replace(".",""),
					"dob": cust.date_of_birth.strftime('%d%m%Y') if cust.date_of_birth else "",
					"gender": "2" if cust.gender == "Male" else "1" if cust.gender == "Female" else "3",
					"pan_no": pan,
					"passport_no": passport,
					"voter_id": voter,
					"driving_license": dl,
					"ration_card_number": ration_card,
					"universal_id_number": aadhar,
					"mobile_no": mobile,
					"email_id1": cust.email_id,
					"address": address.address_line1 if address else "",
					"address2": address.address_line2 if address else "",
					"state_code": address.state_code if address else "",
					"pin_code": address.pincode if address else "",
					"address_category": add_cat,
					"member_code": member_code,
					"member_short_name": member_short_name,
					"current_account_no": loan.name.replace("-","").replace("/",""),
					"account_type": account_type_category_code,
					"ownership_indicator": "1",
					"disbursed": loan.disbursement_date if loan.disbursement_date else "",
					"date_of_last_payment": last_payment_date,
					"date_closed": loan.closure_date if loan.closure_date else "",
					"date_reported":getdate(filters.get("to_date")) if filters.get("to_date") else "", 
					"sanctioned_amount": loan.loan_amount,
					"current_balance": current_balance,
					"amount_overdue": amount_overdue,
					"dpd": dpd,
					"roi": loan.rate_of_interest,
					"tenure": loan.repayment_periods,
					"emi_amount": loan.monthly_repayment_amount,
					"frequency": loan.period_type,
					"income": cust.total_monthly_income,
					})

	
		if filters.get("report_type") == "Commercial" and filters.get("choose_segment"):
			segment = filters["choose_segment"]
			segments_with_loans = ["BS", "AS", "RS", "CR", "GS", "SS", "CD", "TS"]

			# Common loan filters for segments that require them
			common_filters = {
				"workflow_state": ["!=", "Y-Everything Cleared but NOC hold"],
				"docstatus": 1
			}

			loans = []
			if segment in segments_with_loans:
				loans = frappe.db.get_all("Loan", fields=["name", "applicant"], filters=common_filters)

			# Map each segment to its processing function
			segment_function_map = {
				"HD": lambda: hd_data(filters),
				"BS": lambda: bs_data(loans),
				"AS": lambda: as_data(loans),
				"RS": lambda: rs_data(loans),
				"CR": lambda: cr_data(loans),
				"GS": lambda: gs_data(loans),
				"SS": lambda: ss_data(loans),
				"CD": lambda: cd_data(loans),
				"TS": lambda: ts_data(
					bs_data(loans)[1],
					cr_data(loans)[1]
				),
				"Sorting": lambda: sorting_data()
			}

			# Run the selected segment function
			if segment in segment_function_map:
				columns, data = segment_function_map[segment]()

	return columns, data


# def execute(filters = ""):
# 	columns = [
# 		{'fieldname': 'consumer', 'label': 'Consumer Name', 'fieldtype': 'Data', 'width': 150},
# 		{'fieldname': 'dob', 'label': 'Date of Birth', 'fieldtype': 'Data', 'width': 150},
# 		{'fieldname': 'gender', 'label': 'Gender', 'fieldtype': 'Data', 'width': 150},
# 		{'fieldname': 'pan_no', 'label': 'Pan No.', 'fieldtype': 'Data', 'width': 150},
# 		{'fieldname': 'passport_no', 'label': 'Passport No.', 'fieldtype': 'Data', 'width': 150},
# 		{'fieldname': 'passport_issue_date', 'label': 'Passport Issue Date', 'fieldtype': 'Date', 'width': 150},
# 		{'fieldname': 'passport_expiry_date', 'label': 'Passport Expiry Date', 'fieldtype': 'Date', 'width': 150},
# 		{'fieldname': 'voter_id', 'label': 'Voter ID', 'fieldtype': 'Data', 'width': 150},
# 		{'fieldname': 'driving_license', 'label': 'Driving License', 'fieldtype': 'Data', 'width': 150},
# 		{'fieldname': 'driving_license_issue_date', 'label': 'Driving License Issue Date', 'fieldtype': 'Date', 'width': 150},
# 		{'fieldname': 'driving_issue_expiry_date', 'label': 'Driving License Expiry Date', 'fieldtype': 'Date', 'width': 150},
# 		{'fieldname': 'mobile_no', 'label': 'Telephone No.Mobile', 'fieldtype': 'Phone', 'width': 150},
# 		{'fieldname': 'email_id1', 'label': 'Email ID-1', 'fieldtype': 'Data', 'width': 150},
# 		{'fieldname': 'address', 'label': 'Address Line 1', 'fieldtype': 'Data', 'width': 150},
# 		{'fieldname': 'state_code', 'label': 'State Code 1', 'fieldtype': 'Data', 'width': 150},
# 		{'fieldname': 'pin_code', 'label': 'PIN Code 1', 'fieldtype': 'Data', 'width': 150},
# 		{'fieldname': 'sanctioned_amount', 'label': 'High Credit/Sanctioned Amt', 'fieldtype': 'Currency', 'width': 150},
# 		{'fieldname': 'current_balance', 'label': 'Current Balance', 'fieldtype': 'Currency', 'width': 150},
# 		{'fieldname': 'amount_overdue', 'label': 'Amount Overdue', 'fieldtype': 'Currency', 'width': 150},
# 		{'fieldname': 'dpd', 'label': 'No. of Days Past Due', 'fieldtype': 'Int', 'width': 150},
# 		{'fieldname': 'roi', 'label': 'Rate Of Interest', 'fieldtype': 'Percent', 'width': 150},
# 		{'fieldname': 'emi_amount', 'label': 'EMI Amount', 'fieldtype': 'Currency', 'width': 150},
# 		{'fieldname': 'income', 'label': 'Income', 'fieldtype': 'Currency', 'width': 150}
# 	]

# 	# SQL query to join the necessary tables and fetch data
# 	loan_filters = ""
# 	if filters.get("to_date"):
# 		loan_filters = f"AND l.posting_date < '{filters.get('to_date')}'"

# 	query = f"""
# 		SELECT 
# 			c.customer_name AS consumer,
# 			c.date_of_birth AS dob,
# 			CASE WHEN c.gender = 'Male' THEN '2'
# 				WHEN c.gender = 'Female' THEN '1'
# 				ELSE '3' END AS gender,
# 			MAX(CASE WHEN d.document_type = 'PAN Card' THEN d.unmasked_kyc_no END) AS pan_no,
# 			MAX(CASE WHEN d.document_type = 'Passport' THEN d.unmasked_kyc_no END) AS passport_no,
# 			MAX(CASE WHEN d.document_type = 'Voter Id' THEN d.unmasked_kyc_no END) AS voter_id,
# 			MAX(CASE WHEN d.document_type = 'Driving License' THEN d.unmasked_kyc_no END) AS driving_license,
# 			c.mobile_no AS mobile_no,
# 			c.email_id AS email_id1,
# 			a.address_line1 AS address,
# 			a.pincode AS pin_code,
# 			'08' AS state_code,  -- Example, you may need to fetch this dynamically
# 			l.loan_amount AS sanctioned_amount,
# 			SUM(la.payable_principal_amount - la.paid_principal_amount) as current_balance,
# 			IFNULL(la.overdue_amount, 0) AS amount_overdue,
# 			IFNULL(la.dpd, 0) AS dpd,
# 			l.rate_of_interest AS roi,
# 			l.monthly_repayment_amount AS emi_amount,
# 			c.total_monthly_income AS income
# 		FROM 
# 			`tabLoan` l
# 		LEFT JOIN 
# 			`tabCustomer` c ON l.applicant = c.name
# 		LEFT JOIN 
# 			`tabAddress` a ON a.customer = c.name AND a.is_primary_address = 1
# 		LEFT JOIN 
# 			`tabLoan Interest Accrual` la ON la.loan = l.name
# 		LEFT JOIN 
# 			`tabCustomer Document` d ON d.customer = c.name
# 		WHERE 
# 			l.docstatus = 1 AND l.status != 'Closed'
# 			{loan_filters}
# 		GROUP BY 
# 			l.name, c.name
# 	"""

# 	data = frappe.db.sql(query, as_dict=True)

# 	return columns, data

def get_kyc_document(customer_name, doc_type):
	fieldname = "notes" if doc_type == "PAN Card" else "unmasked_kyc_no"
	return frappe.db.get_value(
		"Customer Document", 
		{"parent": customer_name, "document_type": doc_type}, 
		fieldname
	) or ""


def hd_data(filters=None):
	columns = [
		{'fieldname' : 'segment_identifier','label' : 'Segment Identifer','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'member_id','label' : 'Member ID','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'previous_member_id','label' : 'Previous Member ID','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'date_of_creation','label' : 'Date of Creation & Certification of Input File','fieldtype' : 'Date','width' : 150},
		{'fieldname' : 'reporting_date','label' : 'Reporting / Cycle Date','fieldtype' : 'Date','width' : 150},
		{'fieldname' : 'information_type','label' : 'Information Type','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'filler','label' : 'Filler','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'final_formula','label' : 'Final Formula','fieldtype' : 'Data','width' : 150},
	]
	data=[]
	# nbfc_setting=frappe.get_single("NBFC Setting")
	# date_of_creation = getdate(today()).strftime("%d%m%Y")
	# to_date = getdate(filters["to_date"]).strftime("%d%m%Y") if filters and filters.get("to_date") else date_of_creation
	settings = frappe.get_single("NBFC Setting")

	# Select CIC and corresponding member code
	cic = filters.get("choose_cic") if filters else "CIBIL"

	member_code_map = {
		"CIBIL": settings.cibil_member_code,
		"CRIF": settings.crif_member_code,
		"EXPERIAN": settings.experian_member_code,
		"EQUIFAX": settings.equifax_member_code
	}
	member_id = member_code_map.get(cic, settings.cibil_member_code)
	date_of_creation = getdate(today())
	to_date = getdate(filters["to_date"]) if filters and filters.get("to_date") else date_of_creation

	data.append({
		"segment_identifier": "HD",
		"member_id": member_id,
		"date_of_creation": date_of_creation,
		'reporting_date': to_date,
		'information_type': "01",
		"previous_member_id": "",
		"filler": "",
		'final_formula': "final_formula",
	})


	latest_entry = data[-1]

	# Ensure all keys exist before joining
	final_formula_parts = [
		latest_entry.get("segment_identifier") or "",
		latest_entry.get("member_id") or "",
		latest_entry.get("previous_member_id") or "",
		latest_entry.get("date_of_creation").strftime("%d%m%Y") if latest_entry.get("date_of_creation") else "",
		latest_entry.get("reporting_date").strftime("%d%m%Y") if latest_entry.get("reporting_date") else "",
		latest_entry.get("information_type") or "",
		latest_entry.get("filler") or "",
		""
]

	latest_entry["final_formula"] = "|".join(map(str, final_formula_parts))

	return columns,data
def bs_data(loans):
	columns = [
			{'fieldname' : 'ac_no','label' : 'A/c No.','fieldtype' : 'Data','width' : 200},
			{'fieldname' : 'flag','label' : 'Flag','fieldtype' : 'Int','width' : 150},
			{'fieldname' : 'segment_identifier','label' : 'Segment Identifier','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'member_branch_code','label' : 'Member Branch Code','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'previous_member_branch_code','label' : 'Previous Member Branch Code','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'borrower_name','label' : 'Borrower‟s Name','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'borrower_short_name','label' : 'Borrower Short Name','fieldtype' : 'Data','width' : 150},
			# {'fieldname' : 'company_registration_number','label' : 'Company Registration Number','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'date_of_incorporation','label' : 'Date of Incorporation','fieldtype' : 'Date','width' : 150},
			{'fieldname' : 'pan','label' : 'PAN','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'cin','label' : 'CIN','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'tin','label' : 'TIN','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'service_tax','label' : 'Service Tax','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'other_id','label' : 'Other ID','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'borrowers_legal_constitution','label' : 'Borrowers Legal Constitution','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'business_category','label' : 'Business Category','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'business_type','label' : 'Business/ Industry Type','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'class_of_activity1','label' : 'Class of Activity 1','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'class_of_activity2','label' : 'Class of Activity 2','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'class_of_activity3','label' : 'Class of Activity 3','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'sic_code','label' : 'SIC Code','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'financial_year','label' : 'Financial Year','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'number_of_employees','label' : 'Number of Employees','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'credit_rating','label' : 'Credit Rating','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'assesment_agency','label' : 'Assessment Agency / Authority','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'credit_rating_as_on','label' : 'Credit Rating As On','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'credit_rating_expiry_date','label' : 'Credit Rating Expiry Date','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'filler','label' : 'Filler','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'formula1','label' : 'Formula','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'formula2','label' : 'Formula','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'final_formula','label' : 'Final Formula','fieldtype' : 'Data','width' : 150},
			
		]
	legal_constitution_codes = {
			"Private Limited": 11,
			"Public Limited": 12,
			"Business Entities Created by Statute":20,
			"Proprietorship": 30,
			"Partnership": 40,
			"Trust":50,
			"Hindu Undivided Family":55,
			"Co-operative Society": 60,
			"Association of Persons":70,
			"Government": 80,
			"Self Help Group":85,
			"Not Classified":99
		}
	
	business_category_codes={
		"MSME":"01",
		"SME":"02",
		"Micro":"03",
		"Small":"04",
		"Medium":"05",		
		"Large":"06",
		"Others":"07",		
	}
	
	business_type_codes = {
			"Manufacturing": "01",
			"Distribution": "02",
			"Wholesale":"03",
			"Trading":"04",
			"Broking":"05",
			"Service provider":"06",
			"Service":"06",
			"Importing":"07",
			"Exporting":"08",
			"Agriculture":"09",
			"Dealers":"10",
			"Others":"11"
		}

	data = []

	for loan in loans:
		customer = frappe.db.get_value(
			"Customer", 
			{"name": loan["applicant"], "customer_type": "Company"}, 
			["name", "customer_name", "registration_no","date_of_birth","type_of_constitution","date_of_birth"], 
			as_dict=True
			)

		if customer:
			business_details = frappe.get_all(
				"Business Details",
				filters={"parent": customer["name"]},
				fields=["business_category", "industry_type", "no_of_employees"],
				order_by="creation asc",
				limit_page_length=1  
			)

			business_category = ""
			industry_type = ""
			number_of_employees = ""

			if business_details:
				detail = business_details[0]
				business_category = business_category_codes.get(detail.get("business_category"), "")
				industry_type = business_type_codes.get(detail.get("industry_type"), "")
				number_of_employees = detail.get("no_of_employees", "")

			pan_card = get_kyc_document(customer["name"], "PAN Card")

			legal_constitution_code=legal_constitution_codes.get(customer.get("type_of_constitution")," ")
			borrower_name = customer.get("customer_name", "").upper()
			borrower_short_name = borrower_name.split()[0] if borrower_name else ""

			data.append({
				"segment_identifier": "BS",
				# "ac_no": loan["name"], 
				"ac_no": re.sub(r"[^\w]", "", loan["name"]),
				"flag": "1",
				"member_branch_code": "NEW DELHI",
				"previous_member_branch_code":" ",
				"borrower_name": borrower_name,
				"borrower_short_name":borrower_short_name,
				"cin":customer["registration_no"],
				"pan": pan_card,
				"date_of_incorporation": getdate(customer["date_of_birth"]).strftime("%d%m%Y") if customer.get("date_of_birth") else "",
				"borrowers_legal_constitution":legal_constitution_code,
				"business_category": business_category,
				"business_type": industry_type,
				"number_of_employees": number_of_employees,
				"previous_member_branch_code":"",
				# "company_registration_number":"",
				"service_tax":"",
				"other_id":"",
				"class_of_activity1": "",
				"class_of_activity2": "",
				"class_of_activity3": "",
				"sic_code": "",
				"financial_year": "",
				"credit_rating": "",
				"assesment_agency": "",
				"credit_rating_as_on": "",
				"credit_rating_expiry_date": "",
				"filler": "",
				"final_formula":"",	
			})

	if data:
		for entry in data:
			formula1_parts = [
				entry.get("segment_identifier") or "",
				entry.get("member_branch_code") or "",
				entry.get("previous_member_branch_code") or "",
				entry.get("borrower_name") or "",
				entry.get("borrower_short_name") or "",
				entry.get("company_registration_number") or "",
				entry.get("date_of_incorporation") or "",
				entry.get("pan") or "",
				entry.get("cin") or "",
				entry.get("service_tax") or "",
				entry.get("other_id") or "",
				entry.get("borrowers_legal_constitution") or "",
				entry.get("business_category") or "",
				entry.get("business_type") or "",
			]
			entry["formula1"] = "|".join(map(str, formula1_parts))

			formula2_parts = [
				entry.get("class_of_activity1") or "",
				entry.get("class_of_activity2") or "",
				entry.get("class_of_activity3") or "",
				entry.get("sic_code") or "",
				entry.get("financial_year") or "",
				entry.get("number_of_employees") or "",
				entry.get("credit_rating") or "",
				entry.get("assesment_agency") or "",
				entry.get("credit_rating_as_on") or "",
				entry.get("credit_rating_expiry_date") or "",
				entry.get("filler") or ""

			]
			entry["formula2"] = "|".join(map(str, formula2_parts))

			entry["final_formula"] = entry["formula1"] + "|" + entry["formula2"]

	return columns,data

def as_data(loans):
	columns = [
		{'fieldname' : 'ac_no','label' : 'A/c No.','fieldtype' : 'Data','width' : 200},
		{'fieldname' : 'flag','label' : 'Flag','fieldtype' : 'Int','width' : 150},
		{'fieldname' : 'segment_identifier','label' : 'Segment Identifier','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'borrower_office_location','label' : 'Borrower Office Location Type ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'borrower_office_duns_number','label' : 'Borrower Office DUNS Number ','fieldtype' : 'Int','width' : 150},
		{'fieldname' : 'address_line_1','label' : 'Address Line 1 ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'address_line_2','label' : 'Address Line 2 ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'address_line_3','label' : 'Address Line 3 ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'city_town','label' : 'City/Town ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'district','label' : 'District ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'state_union_territory','label' : 'State/Union Territory ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'pincode','label' : 'Pin Code','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'country','label' : 'Country','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'mobile_number','label' : 'Mobile Number(s) ','fieldtype' : 'Phone','width' : 150},
		{'fieldname' : 'telephone_area_code','label' : 'Telephone Area Code ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'telephone_number','label' : 'Telephone Number(s) ','fieldtype' : 'Phone','width' : 150},
		{'fieldname' : 'fax_area_code','label' : 'Fax Area Code ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'fax_number','label' : 'Fax Number(s) ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'filler','label' : 'Filler','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'formula1','label' : 'Formula','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'formula2','label' : 'Formula','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'final_formula','label' : 'Final Formula','fieldtype' : 'Data','width' : 150},
		
	]
	business_location_type = {
			"Registered Office-Required": "01",
			"Branch/Regional Office": "02",
			"Warehouse":"03",
			"Plant/Factory Address":"04",
			"Other":"05"
		}

	data = []
	loans = frappe.db.get_all("Loan", fields=["name","applicant"],filters={"docstatus": 1, "workflow_state": ["!=", "Y-Everything Cleared but NOC hold"]})
	for loan in loans:
		customer = frappe.db.get_value("Customer",
					{"name": loan["applicant"], "customer_type": "Company"},
					["customer_primary_address","mobile_no"],
					as_dict=True
					)

		
		address = ""
		if customer and customer.get("customer_primary_address"):  
			address = frappe.db.get_value(
				"Address",
				{"name": customer["customer_primary_address"]},  
				["address_line1", "address_line2","city","state","country","pincode","district","address_type"],
				as_dict=True
			)

			# if address and address.get("state"):
			state_code = frappe.db.get_value("State", {"state_name": address["state"]}, "state_code","address_type")
			business_location_code = business_location_type.get(address.get("address_type"), "")

		if customer:  
			data.append({
				"segment_identifier": "AS",
				"ac_no": re.sub(r"[^\w]", "", loan["name"]), 
				"flag": "2" ,
				"borrower_office_location":business_location_code if business_location_type else "01",
				"borrower_office_duns_number":"*********",
				"address_line_1":address["address_line1"] or "" if address else "",
				"address_line_2":address["address_line2"] or ""  if address else "",
				# "address_line_3":"",
				"city_town":address["city"] or "" if address else "",
				"district":address["district"] or "" if address else "",
				"state_union_territory":state_code if state_code else "",
				"pincode":address["pincode"] or "" if address else "",
				"mobile_number":re.sub(r'^\+91[-\s]?', '', customer.get("mobile_no") or "").strip(),
				"country":"079",
				"telephone_area_code":"",
				"telephone_number":"",
				"fax_area_code":"",
				'fax_number':"",
				"filler":""

			})

	if data:
		for entry in data:
			formula1_parts = [
				entry.get("segment_identifier") or "",
				# entry.get("ac_no", ""),
				# entry.get("flag", ""),
				entry.get("borrower_office_location") or "",
				entry.get("borrower_office_duns_number") or "",
				entry.get("address_line_1") or "",
				entry.get("address_line_2") or "",
				entry.get("address_line_3") or "",
				entry.get("city_town") or "",
				entry.get("district") or "",
				entry.get("state_union_territory") or "",
				entry.get("pincode") or "",
				entry.get("country") or "",
				entry.get("mobile_number") or "",
				entry.get("telephone_area_code") or "",
				entry.get("telephone_number") or "",
				entry.get("fax_area_code") or ""
			]
			entry["formula1"] = "|".join(map(str, formula1_parts))

			formula2_parts = [
				entry.get("fax_number") or "",
				entry.get("filler") or "",
			]
			entry["formula2"] = "|".join(map(str, formula2_parts))

			entry["final_formula"] = entry["formula1"] + "|" + entry["formula2"]
	return columns,data

def rs_data(loans):
	columns = [
		{'fieldname' : 'ac_no','label' : 'A/c No.','fieldtype' : 'Data','width' : 200},
		{'fieldname' : 'flag','label' : 'Flag','fieldtype' : 'Int','width' : 150},
		{'fieldname' : 'segment_identifier','label' : 'Segment Identifier','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'relationship_duns_no','label' : 'Relationship DUNS Number','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'related_type','label' : 'Related Type ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'relationship','label' : 'Relationship','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'business_entity_name','label' : 'Business Entity Name ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'business_category','label' : 'Business Category ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'business_type','label' : 'Business / Industry Type ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'individual_name_prefix','label' : 'Individual Name Prefix ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'full_name','label' : 'Full Name','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'gender','label' : 'Gender ','fieldtype' : 'Data','width' : 150},
		# {'fieldname' : 'company_reg_no','label' : 'Company Registration Number','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'date_of_incorporation','label' : 'Date of Incorporation ','fieldtype' : 'Phone','width' : 150},
		{'fieldname' : 'date_of_birth','label' : 'Date of Birth ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'pan','label' : 'PAN','fieldtype' : 'Phone','width' : 150},
		{'fieldname' : 'voter_id','label' : 'Voter ID ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'passport_number','label' : 'Passport Number ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'driving_licence_id','label' : 'Driving Licence ID ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'uid','label' : 'UID ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'ration_card_no','label' : 'Ration Card No ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'cin','label' : 'CIN ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'din','label' : 'DIN','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'tin','label' : 'TIN','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'service_tax','label' : 'Service Tax #','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'other_id','label' : 'Other ID','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'percentage_of_control','label' : 'Percentage of Control','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'address_line_1','label' : 'Address Line 1','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'address_line_2','label' : 'Address Line 2 ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'address_line_3','label' : 'Address Line 3 ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'city_town','label' : 'City/Town ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'district','label' : 'District ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'state','label' : 'State/Union Territory ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'pincode','label' : 'Pin Code','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'country','label' : 'Country','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'mobile_number','label' : 'Mobile Number(s) ','fieldtype' : 'Phone','width' : 150},
		{'fieldname' : 'telephone_area_code','label' : 'Telephone Area Code ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'telephone_number','label' : 'Telephone Number(s) ','fieldtype' : 'Phone','width' : 150},
		{'fieldname' : 'fax_area_code','label' : 'Fax Area Code ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'fax_number','label' : 'Fax Number(s) ','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'filler','label' : 'Filler','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'formula1','label' : 'Formula','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'formula2','label' : 'Formula','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'formula3','label' : 'Formula','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'final_formula','label' : 'Final Formula','fieldtype' : 'Data','width' : 150},
		
	]
	relation_with_company={
		"Shareholder":"10",
		"Holding Company":"11",
		"Subsidiary Company":"12",
		"Proprietor":"20",
		"Partner":"30",
		"Trustee":"40",
		"Promoter Director":"51",
		"Nominee Director":"52",
		"Independent Director":"53",
		"Director-Since Resigned":"54",
		"Individual Member of SHG":"55",
		"Other Director":"56",
		"Others":"60",


	}

	data = []

	loans = frappe.db.get_all("Loan", fields=["name", "applicant"], filters={"docstatus": 1, "workflow_state": ["!=", "Y-Everything Cleared but NOC hold"]})

	for loan in loans:
		customer = frappe.db.get_value("Customer", {"name": loan["applicant"], "customer_type": "Company"}, "*", as_dict=True)
		if not customer:
			continue

		relations = frappe.db.get_all(
			"Customer CT", 
			filters={"parent": loan["name"], "parenttype": "Loan", "type": "Guarantor"},
			fields=["customer_name"]
		)

		for r in relations:
			relation_details = frappe.db.get_value("Customer", {"name": r["customer_name"]}, "*", as_dict=True)
			# Get relation with company from child table inside main Customer
			relation_code = ""
			relation_text = ""

			relation_row = frappe.db.get_value(
				"Customer Relation Table",
				{"parent": customer["name"], "customer_name": relation_details["name"]},
				["relation_with_company"],
				as_dict=True
			)

			if relation_row and relation_row.relation_with_company:
				relation_text = relation_row.relation_with_company
				relation_code = relation_with_company.get(relation_text, "")

			
			if not relation_details:
				continue
			
			address = {}
			if relation_details.get("customer_primary_address"):
				address = frappe.db.get_value("Address", {"name": relation_details["customer_primary_address"]}, "*", as_dict=True) or {}

			
			pan_card = get_kyc_document(customer["name"], "PAN Card")
			uid = get_kyc_document(customer["name"], "Aadhar Card")
			ration_card = get_kyc_document(customer["name"], "Ration Card")
			voter_id = get_kyc_document(customer["name"], "Voter ID")
			driving_licence = get_kyc_document(customer["name"], "Driving License")

			entry = {
				"segment_identifier": "RS",
				# "ac_no": loan["name"],
				"related_type": relation_code,
				"relationship": relation_text,
				"business_entity_name": customer.get("customer_name"),
				"ac_no": re.sub(r"[^\w]", "", loan["name"]),
				"flag": 3,
				"relationship_duns_no": "*********",
				"full_name": relation_details.get("customer_name") or "",
				"gender": "01" if relation_details.get("gender") == "Male" else "02" if relation_details.get("gender") == "Female" else "03",
				"cin": customer.get("registration_no") or "",
				"date_of_birth": getdate(relation_details.get("date_of_birth")).strftime("%d%m%Y") if relation_details.get("date_of_birth") else "",
				"address_line_1": address.get("address_line1") or "",
				"address_line_2": address.get("address_line2") or "",
				"city_town": address.get("city") or "",
				"district": address.get("district") or "",
				"state": address.get("state") or "",
				"pincode": address.get("pincode") or "",
				"country":"079",
				"mobile_number": relation_details.get("mobile_no") or "",
				"pan": pan_card,
				"uid": uid.replace("-", "") if uid else "",
				"ration_card_no": ration_card,
				"voter_id": voter_id,
				"driving_licence_id": driving_licence,
				"individual_name_prefix": (
					"Mr." if relation_details.get("gender") == "Male" else
					"Mrs." if relation_details.get("gender") == "Female" and relation_details.get("martial_status") == "Married" else
					"Ms." if relation_details.get("gender") == "Female" and relation_details.get("martial_status") == "Single" else
					""
				),

				"date_of_incorporation": getdate(customer.get("date_of_incorporation")).strftime("%d%m%Y") if customer.get("date_of_incorporation") else "",
				# "company_reg_no": customer.get("registration_no") or "",
			}

			# Formula part building
			formula1_parts = [
				entry.get("segment_identifier", ""), entry.get("relationship_duns_no", ""), entry.get("related_type", ""), entry.get("relationship", ""),
				entry.get("business_entity_name", ""), entry.get("business_category", ""), entry.get("business_type", ""), entry.get("individual_name_prefix", ""),
				entry.get("full_name", ""), entry.get("gender", ""), entry.get("company_reg_no", ""), entry.get("date_of_incorporation", ""),
				entry.get("date_of_birth", ""), entry.get("pan", ""), entry.get("voter_id", "")
			]
			entry["formula1"] = "|".join(formula1_parts)

			formula2_parts = [
				entry.get("passport_number", ""), entry.get("driving_licence_id", ""), entry.get("uid", ""), entry.get("ration_card_no", ""),
				entry.get("cin", ""), entry.get("din", ""), entry.get("tin", ""), entry.get("service_tax", ""), entry.get("other_id", ""),
				entry.get("percentage_of_control", ""), entry.get("address_line_1", ""), entry.get("address_line_2", ""), entry.get("address_line_3", ""),
				entry.get("city_town", ""), entry.get("district", "")
			]
			entry["formula2"] = "|".join(formula2_parts)

			formula3_parts = [
				entry.get("state", ""), entry.get("pincode", ""), entry.get("country", ""),entry.get("mobile_number", ""), entry.get("telephone_area_code", ""),
				entry.get("telephone_number", ""), entry.get("fax_area_code", ""), entry.get("fax_number", ""), entry.get("filler", "")
			]
			entry["formula3"] = "|".join(formula3_parts)

			entry["final_formula"] = "|".join([entry["formula1"], entry["formula2"], entry["formula3"]])

			data.append(entry)

	return columns, data

def cr_data(loans):
	columns = [
		{'fieldname' : 'ac_no','label' : 'A/c No.','fieldtype' : 'Data','width' : 200},
		{'fieldname' : 'flag','label' : 'Flag','fieldtype' : 'Int','width' : 150},
		{'fieldname' : 'segment_identifier','label' : 'Segment Identifier','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'loan_no','label' : 'A/c No.','fieldtype' : 'Data','width' : 200},
		{'fieldname' : 'previous_ac_no','label' : ' Previous Account Number','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'sanction_date','label' : ' Facility / Loan Activation / Sanction Date','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'sanctioned_amount','label' : ' Sanctioned Amount/ Notional Amount of Contract','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'currency_code','label' : 'Currency Code','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'credit_type','label' : ' Credit Type','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'tenure','label' : 'Tenure / Weighted Average maturity period of Contracts','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'repayment_frequency','label' : ' Repayment Frequency','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'drawing_power','label' : 'Drawing Power','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'current_balance','label' : ' Current Balance / Limit Utilized /Mark to Market','fieldtype' : 'Data','width' : 150},
		{"fieldname": "notional_amount", "label": "Notional Amount of Outstanding Restructured Contracts", "fieldtype": "Currency", "width": 150},
		{"fieldname": "loan_expiry_date", "label": "Loan Expiry / Maturity Date", "fieldtype": "Date", "width": 150},
		{"fieldname": "loan_renewal_date", "label": "Loan Renewal Date", "fieldtype": "Date", "width": 150},
		{"fieldname": "asset_classification_ndpd", "label": "Asset Classification / Number of days past due NDPD", "fieldtype": "Data", "width": 150},
		{"fieldname": "asset_classification_date", "label": "Asset Classification Date", "fieldtype": "Date", "width": 150},
		{"fieldname": "amount_overdue", "label": "Amount Overdue / Limit Overdue", "fieldtype": "Currency", "width": 150},
		{"fieldname": "overdue_bucket_01", "label": "Overdue Bucket 01 (1 – 30 days)", "fieldtype": "Currency", "width": 150},
		{"fieldname": "overdue_bucket_02", "label": "Overdue Bucket 02 (31 – 60 days)", "fieldtype": "Currency", "width": 150},
		{"fieldname": "overdue_bucket_03", "label": "Overdue Bucket 03 (61 – 90 days)", "fieldtype": "Currency", "width": 150},
		{"fieldname": "overdue_bucket_04", "label": "Overdue Bucket 04 (91 – 180 days)", "fieldtype": "Currency", "width": 150},
		{"fieldname": "overdue_bucket_05", "label": "Overdue Bucket 05 (Above 180 days)", "fieldtype": "Currency", "width": 150},
		{"fieldname": "high_credit", "label": "High Credit", "fieldtype": "Int", "width": 150},
		{"fieldname": "installment_amount", "label": "Installment Amount", "fieldtype": "Currency", "width": 150},
		{"fieldname": "last_repaid_amount", "label": "Last Repaid Amount", "fieldtype": "Currency", "width": 150},
		{"fieldname": "account_status", "label": "Account Status", "fieldtype": "Data", "width": 150},
		{"fieldname": "account_status_date", "label": "Account Status Date", "fieldtype": "Date", "width": 150},
		{"fieldname": "written_off_amount", "label": "Written Off Amount", "fieldtype": "Currency", "width": 150},
		{"fieldname": "settled_amount", "label": "Settled Amount", "fieldtype": "Currency", "width": 150},
		{"fieldname": "restructuring_reason", "label": "Major Reasons for Restructuring", "fieldtype": "Data", "width": 150},
		{"fieldname": "npa_contracts_amount", "label": "Amount of Contracts Classified as NPA", "fieldtype": "Currency", "width": 150},
		{"fieldname": "asset_security_coverage", "label": "Asset Based Security Coverage", "fieldtype": "Data", "width": 150},
		{"fieldname": "guarantee_coverage", "label": "Guarantee Coverage", "fieldtype": "Data", "width": 150},
		{"fieldname": "bank_remark_code", "label": "Bank Remark Code", "fieldtype": "Data", "width": 150},
		{"fieldname": "wilful_default_status", "label": "Wilful Default Status", "fieldtype": "Int", "width": 150},
		{"fieldname": "wilful_default_date", "label": "Date Classified as Wilful Default", "fieldtype": "Date", "width": 150},
		{"fieldname": "suit_filed_status", "label": "Suit Filed Status", "fieldtype": "Data", "width": 150},
		{"fieldname": "suit_reference_number", "label": "Suit Reference Number", "fieldtype": "Data", "width": 150},
		{"fieldname": "suit_amount", "label": "Suit Amount in Rupees", "fieldtype": "Currency", "width": 150},
		{"fieldname": "suit_date", "label": "Date of Suit", "fieldtype": "Date", "width": 150},
		{"fieldname": "dispute_id", "label": "Dispute ID No.", "fieldtype": "Data", "width": 150},
		{"fieldname": "transaction_type_code", "label": "Transaction Type Code", "fieldtype": "Data", "width": 150},
		{'fieldname' : 'other_bk','label' : 'OTHER_BK','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'ufce','label' : 'UFCE (Amount)','fieldtype' : 'Currency','width' : 150},
		{'fieldname' : 'ufce_date','label' : 'UFCE Date','fieldtype' : 'Date','width' : 150},
		{'fieldname' : 'formula1','label' : 'Formula','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'formula2','label' : 'Formula','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'formula3','label' : 'Formula','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'final_formula','label' : 'Final Formula','fieldtype' : 'Data','width' : 150},
			
		
	]
	account_status_codes = {
		"Open": "01",
		"L-Live":"01",
		"N-NPA":"01",
		"Closed By Payment": "02",
		"Settled & Closed": "03",
		"Restructured": "04",
		"Written Off": "05",
		"Settled Post Write Off": "06",
		"Invoked": "07",
		"Devolved": "08",
		"Restructured Due to Natural Calamity": "09",
		"Sold to ARC (refer Annex. B for validations)": "10",
		"Purchase from Bank": "11"
	}

	repayment_frequency_code={
		"Monthly":"01",
		"Quarterly":"02",
		"Half yearly":"03",
		"Annual":"04",
		"On Demand":"05",
		"Bullet":"06",
		"Rolling":"07",
		"Others":"08"
	}
	credit_type_code = {
    "Auto Loan": "9001",
    "Loan Against Property": "9002",
    "Gold Loan": "9003",
	"Personal Loan":"",

	}

	sub_product_code = {
		"Unsecured Business Loan": "5000",
		"Secured Business Loan": "5001"
	}
	
	data = []
	loans = frappe.db.get_all("Loan", 
	fields=[
		"name", "applicant", "loan_application", "period_type", 
		"loan_amount", "repayment_periods", "posting_date", 
		"instalment_end_date", "monthly_repayment_amount","product_category","product","workflow_state"
	],
	filters={
		"docstatus": 1,
		"workflow_state": ["!=", "Y-Everything Cleared but NOC hold"]
	}
	)
		
	agewise_filters = {
		"show_terminated_loans": False,
		"emi_type": "All",
		"applicant_name": None
	}

	agewise_data = get_agewise_overdue_data(agewise_filters)

	# Map data by loan number
	overdue_map = {}
	for row in agewise_data:
		loan_no = row.get("loan_no")
		if loan_no:
			if loan_no not in overdue_map:
				overdue_map[loan_no] = {
					"balance": 0,
					"0_30_days": 0,
					"31_60_days": 0,
					"61_90_days": 0,
					"above_90_days": 0
				}
			overdue_map[loan_no]["balance"] += flt(row.get("balance", 0))
			overdue_map[loan_no]["0_30_days"] += flt(row.get("0_30_days", 0))
			overdue_map[loan_no]["31_60_days"] += flt(row.get("31_60_days", 0))
			overdue_map[loan_no]["61_90_days"] += flt(row.get("61_90_days", 0))
			overdue_map[loan_no]["above_90_days"] += flt(row.get("above_90_days", 0))

	for loan in loans:
		customer = frappe.db.get_value("Customer", {"name": loan["applicant"], "customer_type": "Company"}, "name")

		repayment = repayment_frequency_code.get(loan.get("period_type"), "")

		account_status = account_status_codes.get(loan.get("workflow_state"),"")
		

		if loan.get("product_category") == "MSME":
			credit_type = sub_product_code.get(loan.get("product"), "")
		else:
			credit_type = credit_type_code.get(loan.get("product_category"), "")

		today = getdate(nowdate())

		accrual_entries = frappe.get_all(
			"Loan Interest Accrual",
			filters={
				"loan": loan["name"],
				"docstatus": 1
			},
			fields=[
				"payable_principal_amount",
				"interest_amount",
				"paid_principal_amount",
				"paid_interest_amount",
				"posting_date"
			]
		)
		total_paid_principal = sum(flt(row["paid_principal_amount"]) for row in accrual_entries)
		current_balance = flt(loan["loan_amount"]) - total_paid_principal

		last_repayment = frappe.get_all("Loan Repayment",
			filters={"against_loan": loan["name"], "docstatus": 1},
			fields=["posting_date", "_amount_paid"],
			order_by="posting_date desc",
			limit_page_length=1
		)

		last_repaid_amount = flt(last_repayment[0]["_amount_paid"]) if last_repayment else 0



		overdue_buckets = {
			"overdue_bucket_01": 0,
			"overdue_bucket_02": 0,
			"overdue_bucket_03": 0,
			"overdue_bucket_04": 0,
			"overdue_bucket_05": 0
		}
		amount_overdue = 0

		overdue = overdue_map.get(loan["name"])
		if overdue:
			amount_overdue = overdue.get("balance", 0)
			overdue_buckets["overdue_bucket_01"] = overdue.get("0_30_days", 0)
			overdue_buckets["overdue_bucket_02"] = overdue.get("31_60_days", 0)
			overdue_buckets["overdue_bucket_03"] = overdue.get("61_90_days", 0)
			overdue_buckets["overdue_bucket_04"] = overdue.get("above_90_days", 0)
																																													

		if customer:
			data.append({
				"segment_identifier": "CR",
				# "ac_no": loan["name"],
				"ac_no": re.sub(r"[^\w]", "", loan["name"]),
				"loan_no": re.sub(r"[^\w]", "", loan["name"]),
				"flag": "4",
				"sanction_date": getdate(loan["posting_date"]).strftime("%d%m%Y") if loan.get("posting_date") else "",
				"sanctioned_amount": loan["loan_amount"],
				"currency_code": "INR",
				"tenure": loan["repayment_periods"],
				"repayment_frequency": repayment,
				"credit_type": credit_type,
				"drawing_power": loan["loan_amount"],
				"loan_expiry_date": getdate(loan["instalment_end_date"]).strftime("%d%m%Y") if loan.get("instalment_end_date") else "",
				"installment_amount": loan["monthly_repayment_amount"],
				"asset_classification_ndpd":"0001",
				"amount_overdue":amount_overdue ,
				"wilful_default_status":0,
				"last_repaid_amount":last_repaid_amount,
				"current_balance": current_balance,
				"account_status": account_status,
				**overdue_buckets
			})


		if data: 
			for entry in data:
				formula1_parts = [
				entry.get("segment_identifier") or "",
				entry.get("ac_no") or "",
				# entry.get("flag", ""),
				entry.get("loan_no") or "",
				entry.get("sanction_date") or "",
				entry.get("sanctioned_amount") or "",
				entry.get("currency_code") or "",
				entry.get("tenure") or "",
				entry.get("repayment_frequency") or "",
				entry.get("drawing_power") or "",
				entry.get("current_balance") or "",
				entry.get("credit_type") or "",
				entry.get("notional_amount") or "",
				entry.get("loan_expiry_date") or "",
				entry.get("loan_renewal_date") or "",
				entry.get("asset_classification_ndpd") or "",
			]

			formula2_parts = [
				entry.get("asset_classification_date") or "",
				entry.get("amount_overdue") or "",
				entry.get("overdue_bucket_01") or "",
				entry.get("overdue_bucket_02") or "",
				entry.get("overdue_bucket_03") or "",
				entry.get("overdue_bucket_04") or "",
				entry.get("overdue_bucket_05") or "",
				entry.get("high_credit") or "",
				entry.get("installment_amount") or "",
				entry.get("last_repaid_amount") or "",
				entry.get("account_status") or "",
				entry.get("account_status_date") or "",
				entry.get("written_off_amount") or "",
				entry.get("settled_amount") or "",
				entry.get("restructuring_reason") or "",
			]

			formula3_parts = [
			entry.get("npa_contracts_amount") or "",
			entry.get("asset_security_coverage") or "",
			entry.get("guarantee_coverage") or "",
			entry.get("bank_remark_code") or "",
			entry.get("wilful_default_status") or "",
			entry.get("wilful_default_date") or "",
			entry.get("suit_filed_status") or "",
			entry.get("suit_reference_number") or "",
			entry.get("suit_amount") or "",
			entry.get("suit_date") or "",
			entry.get("dispute_id") or "",
			entry.get("transaction_type_code") or "",
			entry.get("other_bk") or "",
			entry.get("ufce") or "",
			entry.get("ufce_date") or "",
		]

			entry["formula1"] = "|".join(map(str, formula1_parts))
			entry["formula2"] = "|".join(map(str, formula2_parts))
			entry["formula3"] = "|".join(map(str, formula3_parts))

			entry["final_formula"] = "|".join([entry["formula1"], entry["formula2"], entry["formula3"]])

	return columns,data
def gs_data(loans):
	columns = [
			{'fieldname' : 'ac_no','label' : 'A/c No.','fieldtype' : 'Data','width' : 200},
			{'fieldname' : 'flag','label' : 'Flag','fieldtype' : 'Int','width' : 150},
			{'fieldname' : 'segment_identifier','label' : 'Segment Identifier','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'gurantor_duns_number','label' : 'Guarantor DUNS Number ','fieldtype' : 'Int','width' : 150},
			{'fieldname' : 'gurantor_type','label' : 'Guarantor Type','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'business_category','label' : 'Business Category ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'business_type','label' : 'Business / Industry Type ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'gurantor_entity_name','label' : 'Guarantor Entity Name ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'individual_name_prefix','label' : 'Individual Name Prefix ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'full_name','label' : 'Full Name','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'gender','label' : 'Gender ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'company_reg_no','label' : 'Company Registration Number','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'date_of_incorporation','label' : 'Date of Incorporation ','fieldtype' : 'Phone','width' : 150},
			{'fieldname' : 'date_of_birth','label' : 'Date of Birth ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'pan','label' : 'PAN','fieldtype' : 'Phone','width' : 150},
			{'fieldname' : 'voter_id','label' : 'Voter ID ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'passport_number','label' : 'Passport Number ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'driving_licence_id','label' : 'Driving Licence ID ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'uid','label' : 'UID ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'ration_card_no','label' : 'Ration Card No ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'cin','label' : 'CIN ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'din','label' : 'DIN','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'tin','label' : 'TIN','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'service_tax','label' : 'Service Tax #','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'other_id','label' : 'Other ID','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'address_line_1','label' : 'Address Line 1','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'address_line_2','label' : 'Address Line 2 ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'address_line_3','label' : 'Address Line 3 ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'city_town','label' : 'City/Town ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'district','label' : 'District ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'state_union_territory','label' : 'State/Union Territory ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'pincode','label' : 'Pin Code','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'country','label' : 'Country','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'mobile_number','label' : 'Mobile Number(s) ','fieldtype' : 'Phone','width' : 150},
			{'fieldname' : 'telephone_area_code','label' : 'Telephone Area Code ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'telephone_number','label' : 'Telephone Number(s) ','fieldtype' : 'Phone','width' : 150},
			{'fieldname' : 'fax_area_code','label' : 'Fax Area Code ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'fax_number','label' : 'Fax Number(s) ','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'filler','label' : 'Filler','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'formula1','label' : 'Formula','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'formula2','label' : 'Formula','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'formula3','label' : 'Formula','fieldtype' : 'Data','width' : 150},
			{'fieldname' : 'final_formula','label' : 'Final Formula','fieldtype' : 'Data','width' : 150},
			
		]
	
	guarantor_type_codes = {
		"Business Entity Registered in India": "1",
		"Resident Indian Individual": "2",
		"Business Entity Registered Outside India": "3",
		"Foreign/ Non-Resident Indian Individual": "4"
	}

	
	business_category_codes={
		"MSME":"01",
		"SME":"02",
		"Micro":"03",
		"Small":"04",
		"Medium":"05",		
		"Large":"06",
		"Others":"07",		
	}
	
	business_type_codes = {
			"Manufacturing": "01",
			"Distribution": "02",
			"Wholesale":"03",
			"Trading":"04",
			"Broking":"05",
			"Service provider":"06",
			"Service":"06",
			"Importing":"07",
			"Exporting":"08",
			"Agriculture":"09",
			"Dealers":"10",
			"Others":"11"
	}

	relation_with_company={
		"Shareholder":"10",
		"Holding Company":"11",
		"Subsidiary Company":"12",
		"Proprietor":"20",
		"Partner":"30",
		"Trustee":"40",
		"Promoter Director":"51",
		"Nominee Director":"52",
		"Independent Director":"53",
		"Director-Since Resigned":"54",
		"Individual Member of SHG":"55",
		"Other Director":"56",
		"Others":"60",


	}

	data = []
	loans = frappe.db.get_all("Loan", fields=["name","applicant"],filters={"docstatus": 1, "workflow_state": ["!=", "Y-Everything Cleared but NOC hold"]})
	for loan in loans:
		customer = frappe.db.get_value("Customer", {"name": loan["applicant"], "customer_type": "Company"}, "name")
		if not customer:
			continue

		# relations = frappe.db.get_all(
		# 	"Customer CT", 
		# 	filters={"parent": loan["name"], "parenttype": "Loan", "type": "Guarantor"},
		# 	fields=["customer_name"]
		# )

		# for r in relations:
		# 	relation_details = frappe.db.get_value("Customer", {"name": r["customer_name"]}, "*", as_dict=True)
		# 	relation_code = ""
		# 	relation_text = ""

		# 	relation_row = frappe.db.get_value(
		# 		"Customer Relation Table",
		# 		{"parent": customer, "customer_name": relation_details["name"]},
		# 		["relation_with_company"],
		# 		as_dict=True
		# 	)

		# 	if relation_row and relation_row.relation_with_company:
		# 		relation_text = relation_row.relation_with_company
		# 		relation_code = relation_with_company.get(relation_text, "")

			
		# 	if not relation_details:
		# 		continue																								

		

		guarantors = frappe.db.get_all(
			"Customer CT", 
			filters={"parent": loan["name"], "parenttype": "Loan", "type": "Guarantor"},
			fields=["customer_name","gurantor_type"]
		)
		
		
		
		for guarantor in guarantors:
			guarantor_details = frappe.db.get_value(
				"Customer", 
				{"name": guarantor["customer_name"]}, 
				["customer_name","gender","date_of_birth","customer_primary_address","martial_status"],
				as_dict=True
			)
			guarantor_type_text = guarantor.get("gurantor_type", "") 
			guarantor_type_code = guarantor_type_codes.get(guarantor_type_text, "")

			business_details = frappe.get_all(
				"Business Details",
				filters={"parent": guarantor["customer_name"]},
				fields=["business_category", "industry_type", "no_of_employees"],
				order_by="creation asc",
				limit_page_length=1  
			)

			business_category = ""
			industry_type = ""

			if business_details:
				detail = business_details[0]
				business_category = business_category_codes.get(detail.get("business_category"), "")
				industry_type = business_type_codes.get(detail.get("industry_type"), "")
			
			
			address = ""
			if guarantor_details and guarantor_details.get("customer_primary_address"):  
				address = frappe.db.get_value(
					"Address",
					{"name": guarantor_details["customer_primary_address"]},
					["address_line1", "address_line2", "city", "state", "country", "pincode", "district"],
					as_dict=True
				)
			
			
			pan_card = get_kyc_document(customer, "PAN Card")
			uid = get_kyc_document(customer, "Aadhar Card")
			ration_card = get_kyc_document(customer, "Ration Card")
			voter_id = get_kyc_document(customer, "Voter ID")
			driving_licence = get_kyc_document(customer, "Driving License")
			if guarantor_details:
				data.append({
					"segment_identifier": "GS",
					"ac_no": re.sub(r"[^\w]", "", loan["name"]),
					"flag": "5",
					"gurantor_duns_number":"*********",
					"full_name": guarantor_details["customer_name"],
					"gender": "01" if guarantor_details.get("gender", "")== "Male" else "02" if guarantor_details.get("gender", "")== "Female" else "",
					"date_of_birth": guarantor_details["date_of_birth"].strftime("%d%m%Y") if guarantor_details.get("date_of_birth") else "",
					"address_line_1":address["address_line1"] if address else "",
					"address_line_2":address["address_line2"] if address else "",
					"city_town":address["city"] if address else "",
					"district":address["district"] if address else "",
					"state":address["state"] if address else "",
					"pincode":address["pincode"] if address else "",
					"country":"079",
					"business_category":business_category,
					"business_type": industry_type,
					"pan": pan_card,
					"uid": uid.replace("-", "") if uid else "",
					"gurantor_type": guarantor_type_code,
					"ration_card_no": ration_card,
					"voter_id": voter_id,
					"driving_licence_id": driving_licence,
					"individual_name_prefix": (
						"Mr." if guarantor_details.get("gender") == "Male" else
						"Mrs." if guarantor_details.get("gender") == "Female" and guarantor_details.get("martial_status") == "Married" else
						"Ms." if guarantor_details.get("gender") == "Female" and guarantor_details.get("martial_status") == "Single" else
						""
					),

					
				})
	# frappe.msgprint(repr(data))

	if data:
		for entry in data:
			formula1_parts = [
			entry.get("segment_identifier") or "",
			# entry.get("ac_no", ""),
			# entry.get("flag", ""),
			entry.get("gurantor_duns_number") or "",
			entry.get("gurantor_type") or "",
			entry.get("business_category") or "",
			entry.get("business_type") or "",
			entry.get("gurantor_entity_name") or "",
			entry.get("individual_name_prefix") or "",
			entry.get("full_name") or "",
			entry.get("gender") or "",
			entry.get("company_reg_no") or "",
			entry.get("date_of_incorporation") or "",
			entry.get("date_of_birth") or "",
			entry.get("pan") or "",
			entry.get("voter_id") or "",
			entry.get("passport_number") or "",
		]

			formula2_parts = [
				entry.get("driving_licence_id") or "",
				entry.get("uid") or "",
				entry.get("ration_card_no") or "",
				entry.get("cin") or "",
				entry.get("din") or "",
				entry.get("tin") or "",
				entry.get("service_tax") or "",
				entry.get("other_id") or "",
				entry.get("address_line_1") or "",
				entry.get("address_line_2") or "",
				entry.get("address_line_3") or "",
				entry.get("city_town") or "",
				entry.get("district") or "",
				entry.get("state_union_territory") or "",
				entry.get("pincode") or "",
				entry.get("country") or "",
			]

			formula3_parts = [
				entry.get("mobile_number") or "",
				entry.get("telephone_area_code") or "",
				entry.get("telephone_number") or "",
				entry.get("fax_area_code") or "",
				entry.get("fax_number") or "",
				entry.get("filler") or "",
			]

			
			entry["formula1"] = "|".join(map(str, formula1_parts))
			entry["formula2"] = "|".join(map(str, formula2_parts))
			entry["formula3"] = "|".join(map(str, formula3_parts))

		
			entry["final_formula"] = "|".join([entry["formula1"], entry["formula2"], entry["formula3"]])	
	return columns,data

def ss_data(loans):
	columns = [
		{'fieldname': 'ac_no', 'label': 'A/c No.', 'fieldtype': 'Data', 'width': 200},
		{'fieldname': 'flag', 'label': 'Flag', 'fieldtype': 'Int', 'width': 150},
		{'fieldname': 'segment_identifier', 'label': 'Segment Identifier', 'fieldtype': 'Data', 'width': 150},
		{'fieldname': 'value_of_security', 'label': 'Value of Security', 'fieldtype': 'Currency', 'width': 150},
		{'fieldname': 'currency_type', 'label': 'Currency Type', 'fieldtype': 'Data', 'width': 150},
		{'fieldname': 'type_of_security', 'label': 'Type of Security', 'fieldtype': 'Data', 'width': 150},
		{'fieldname': 'security_classification', 'label': 'Security Classification', 'fieldtype': 'Data', 'width': 150},
		{'fieldname': 'date_of_valuation', 'label': 'Date of Valuation', 'fieldtype': 'Date', 'width': 150},
		{'fieldname': 'filler', 'label': 'Filler', 'fieldtype': 'Data', 'width': 150},
		{'fieldname': 'final_formula', 'label': 'Final Formula', 'fieldtype': 'Data', 'width': 150},
	]

	security_type_dict = {
		"Cash/ Bullion/ Bank Deposits": "001",
		"Shares/ Bonds/ Securities": "002",
		"Inventory (Raw Material, WIP and Finished Goods)": "003",
		"Accounts Receivable": "004",
		"Other Current Assets": "005",
		"Plant & Machinery and Equipment": "006",
		"Land & Buildings": "007",
		"Other Fixed Assets": "008",
		"Other Assets": "009",
		"Aggregate of all Current Assets": "010",
		"Aggregate of all Fixed Assets": "011"
	}

	security_classification_dict = {
		"Primary – First Charge": "01",
		"Primary - Second Charge": "02",
		"Primary - Third Charge": "03",
		"Primary - Parri Passu": "04",
		"Collateral - First Charge": "21",
		"Collateral - Second Charge": "22",
		"Collateral - Third Charge": "23",
		"Collateral - Parri Passu": "24"
	}

	data = []

	loans = frappe.db.get_all("Loan", fields=["name", "applicant", "asset_type"],
		filters={"docstatus": 1, "workflow_state": ["!=", "Y-Everything Cleared but NOC hold"]})

	for loan in loans:
		customer = frappe.db.get_value("Customer", {"name": loan["applicant"], "customer_type": "Company"}, "name")
		if not customer:
			continue

		security_details = frappe.get_all(
			"Security CT",
			filters={"parent": loan["name"], "parenttype": "Loan"},
			fields=["security_classification", "type_of_security", "value_of_security", "date_of_valuation"]
		)

		# if security details found, add one row for each
		if security_details:
			for sd in security_details:
				data.append({
					"segment_identifier": "SS",
					"ac_no": re.sub(r"[^\w]", "", loan["name"]),
					"flag": 6,
					"value_of_security": sd.get("value_of_security"),
					"currency_type": "INR",
					"type_of_security": security_type_dict.get(sd.get("type_of_security"), ""),
					"security_classification": security_classification_dict.get(sd.get("security_classification"), ""),
					"date_of_valuation": sd.get("date_of_valuation"),
					"filler": "",
				})
		else:
			# still show the loan even if security details missing
			data.append({
				"segment_identifier": "SS",
				"ac_no": re.sub(r"[^\w]", "", loan["name"]),
				"flag": 6,
				"value_of_security": None,
				"currency_type": "INR",
				"type_of_security": "",
				"security_classification": "",
				"date_of_valuation": None,
				"filler": "",
			})

	# create final_formula
	for entry in data:
		final_formula_parts = [
			entry.get("segment_identifier") or "",
			entry.get("value_of_security") or "",
			entry.get("currency_type") or "",
			entry.get("type_of_security") or "",
			entry.get("security_classification") or "",
			entry.get("date_of_valuation") or "",
			entry.get("filler") or "",
		]
		entry["final_formula"] = "|".join(map(str, final_formula_parts))

	return columns, data

def cd_data(loans):
	columns = [
		{'fieldname' : 'ac_no','label' : 'A/c No.','fieldtype' : 'Data','width' : 200},
		{'fieldname' : 'flag','label' : 'Flag','fieldtype' : 'Int','width' : 150},
		{'fieldname' : 'segment_identifier','label' : 'Segment Identifier','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'date_of_dishonour','label' : 'Date of Dishonour','fieldtype' : 'Date','width' : 150},
		{'fieldname' : 'amount','label' : 'Amount','fieldtype' : 'Currency','width' : 150},
		{'fieldname' : 'cheque_number','label' : 'Instrument / Cheque Number','fieldtype' : 'Int','width' : 150},
		{'fieldname' : 'number_of_times_dishonoured','label' : 'Number of times dishonoured','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'cheque_issue_date','label' : 'Cheque Issue Date','fieldtype' : 'Date','width' : 150},
		{'fieldname' : 'reason_for_dishonour','label' : 'Reason for Dishonour','fieldtype' : 'Date','width' : 150},
		{'fieldname' : 'filler','label' : 'Filler','fieldtype' : 'Data','width' : 150},
		{'fieldname' : 'final_formula','label' : 'Final Formula','fieldtype' : 'Data','width' : 150},
		
		
	]

	data = []
	loans = frappe.db.get_all("Loan", fields=["name","applicant"],filters={"docstatus": 1, "workflow_state": ["!=", "Y-Everything Cleared but NOC hold"]})
	for loan in loans:
		customer = frappe.db.get_value("Customer", {"name": loan["applicant"], "customer_type": "Company"}, "name")

		if customer:  
			data.append({
				"segment_identifier": "CD",
				# "ac_no": loan["name"], 
				"ac_no": re.sub(r"[^\w]", "", loan["name"]), 
	
			})
	if data:  
		for entry in data:
			final_formula_parts = [
				entry.get("segment_identifier") or "",
				entry.get("date_of_dishonour") or "",
				entry.get("amount") or "",
				entry.get("cheque_number") or "",
				entry.get("number_of_times_dishonoured") or "",
				entry.get("cheque_issue_date") or "",
				entry.get("reason_for_dishonour") or "",
				entry.get("filler") or "",
				
			]

			entry["final_formula"] = "|".join(map(str, final_formula_parts))	
	return columns,data
def ts_data(bs, cr):
	columns = [
		{'fieldname': 'segment_identifier', 'label': 'Segment Identifier', 'fieldtype': 'Data', 'width': 150},
		{'fieldname': 'no_of_borrower_segment', 'label': 'Number of Borrower Segments', 'fieldtype': 'Int', 'width': 150},
		{'fieldname': 'no_of_credit_facility_segment', 'label': 'Number of Credit Facility Segments', 'fieldtype': 'Int', 'width': 150},
		{'fieldname': 'filler', 'label': 'Filler', 'fieldtype': 'Data', 'width': 150},
		{'fieldname': 'final_formula', 'label': 'Final Formula', 'fieldtype': 'Data', 'width': 300},
	]

	data = []
	data.append({
		"segment_identifier": "TS",
		"no_of_borrower_segment": len(bs),
		"no_of_credit_facility_segment": len(cr),
		"filler": ""
	})

	if data:
		latest_entry = data[-1]
		final_formula_parts = [
			latest_entry["segment_identifier"] or "",
			str(latest_entry["no_of_borrower_segment"]) or "",
			str(latest_entry["no_of_credit_facility_segment"]) or "",
			""
		]
		latest_entry["final_formula"] = "|".join(final_formula_parts)
	return columns, data

def sorting_data():
    loans = frappe.db.get_all("Loan", fields=["name", "applicant"],filters={"docstatus": 1, "workflow_state": ["!=", "Y-Everything Cleared but NOC hold"]})

    bs_cols, bs = bs_data(loans)
    as_cols, as_seg = as_data(loans)
    rs_cols, rs = rs_data(loans)
    cr_cols, cr = cr_data(loans)
    gs_cols, gs = gs_data(loans)
    ss_cols, ss = ss_data(loans)

    # Merge all segments
    all_segments = bs + as_seg + rs + cr + gs + ss

    # Clean decimals like "2500000.0" → "2500000"
    def strip_decimal_zeros(value):
        return re.sub(r'(\d+)\.0(\D|$)', r'\1\2', value)

    # Group data by ac_no
    grouped = {}

    for row in all_segments:
        ac_no = row.get("ac_no")
        if not ac_no:
            continue

        # Clean ac_no of special chars
        ac_no_clean = re.sub(r'[^\w]', '', ac_no)
        row["ac_no"] = ac_no_clean

        # Clean final formula
        raw_formula = row.get("final_formula", "").replace(ac_no, ac_no_clean)
        clean_formula = strip_decimal_zeros(raw_formula)
        row["final_formula"] = clean_formula

        grouped.setdefault(ac_no_clean, []).append({
            "ac_no": ac_no_clean,
            "flag": row.get("flag"),  # placeholder, will reset later
            "segment_identifier": row.get("segment_identifier"),
            "final_formula": clean_formula
        })

    # Final list to store sorted & flagged data
    sorting_data = []

    # Segment display order
    segment_order = {
        "BS": 1,
        "AS": 2,
        "RS": 3,
        "CR": 4,
        "GS": 5,
        "SS": 6
    }

    # Sort segments per A/c No and reassign flag values (1 → 6)
    for loan_id in sorted(grouped.keys()):
        segments = sorted(
            grouped[loan_id],
            key=lambda x: segment_order.get(x.get("segment_identifier", ""), 99)
        )

        for seg in segments:
            seg["flag"] = segment_order.get(seg.get("segment_identifier", ""), 99)
            sorting_data.append(seg)
    # Final column headers
    columns = [
        {'fieldname': 'ac_no', 'label': 'A/c No.', 'fieldtype': 'Data', 'width': 200},
        {'fieldname': 'flag', 'label': 'Flag', 'fieldtype': 'Int', 'width': 200},
        {'fieldname': 'segment_identifier', 'label': 'Segment Identifier', 'fieldtype': 'Data', 'width': 200},
        {'fieldname': 'final_formula', 'label': 'Final Formula', 'fieldtype': 'Data', 'width': 600},
    ]

    return columns, sorting_data


@frappe.whitelist()
def download_cibil_segments_txt():
    final_data = []

    # 1. ----- HD Segment
    _, hd_data_rows = hd_data()
    if hd_data_rows:
        final_data.append(hd_data_rows[0].get("final_formula", ""))

    # 2. ----- Sorting Segments (BS, AS, RS, CR, GS, SS) in order
    _, sorted_segments = sorting_data()
    for seg in sorted_segments:
        final_data.append(seg.get("final_formula", ""))

    # 3. ----- CD Segment (only if valid)
    loans = frappe.db.get_all("Loan", fields=["name", "applicant"], filters={"docstatus": 1, "workflow_state": ["!=", "Y-Everything Cleared but NOC hold"]})
    # _, cd_data_rows = cd_data(loans)

    # valid_cd_rows = []
    # for row in cd_data_rows:
    #     final_formula = row.get("final_formula", "").strip()
    #     if final_formula:
    #         valid_cd_rows.append(final_formula)

    # if valid_cd_rows:
    #     final_data.extend(valid_cd_rows)

    # 4. ----- TS Segment (Trailer)
    _, bs_data_rows = bs_data(loans)
    _, cr_data_rows = cr_data(loans)
    _, ts_data_rows = ts_data(bs_data_rows, cr_data_rows)
    if ts_data_rows:
        final_data.append(ts_data_rows[0].get("final_formula", ""))

    # Write to text
    output_txt = "\n".join(final_data)

    buffer = BytesIO()
    buffer.write(output_txt.encode("utf-8"))
    buffer.seek(0)

    frappe.response["filename"] = "cibil_segments.txt"
    frappe.response["filecontent"] = buffer.read()
    frappe.response["type"] = "download"
